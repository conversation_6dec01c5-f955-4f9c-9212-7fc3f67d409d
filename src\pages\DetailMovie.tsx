import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Movie } from '../types/movies';
import { getCachedData } from '../utils/tizenStorage';
import { formatTime } from '../utils/formatTime';
import { useTranslation } from '../hooks/useTranslation';
import { RETURN_KEY, RETURN_KEY_CODE } from '../utils/keysCode';

const MovieDescription = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const media = state?.media as Movie;
  const navigationState = state?.navigationState;
  const [selectedButton, setSelectedButton] = useState<'play' | 'resume' | 'trailer'>('play');
  const [hasResumePoint, setHasResumePoint] = useState(false);
  const [resumePosition, setResumePosition] = useState<number | null>(null);
  const [isTrailerPlaying, setIsTrailerPlaying] = useState(false);
  const trailerTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Vérifier s'il existe une position de reprise pour ce film
  useEffect(() => {
    const checkResumePoint = async () => {
      if (!media || !media.link) return;

      try {
        const savedPosition = await getCachedData<{
          position: number;
          timestamp: number;
          duration: number;
        }>(`movie_position_${media.link}`);

        if (savedPosition && savedPosition.position > 0) {
          // Vérifier si la position sauvegardée est récente (moins de 30 jours)
          const isRecent = Date.now() - savedPosition.timestamp < 30 * 24 * 60 * 60 * 1000;

          // Vérifier si la position n'est pas trop proche du début ou de la fin
          const isSignificant = savedPosition.position > 30000 && // Plus de 30 secondes
            savedPosition.duration > 0 &&
            savedPosition.position < savedPosition.duration * 0.95; // Pas dans les 5% finaux

          if (isRecent && isSignificant) {
            setHasResumePoint(true);
            setResumePosition(savedPosition.position);
          }
        }
      } catch (error) {
        console.error('Error checking resume point:', error);
      }
    };

    checkResumePoint();
  }, [media]);

  // Démarrer automatiquement le trailer après 6 secondes
  useEffect(() => {
    if (!media?.link) return;

    // Démarrer le timer pour lire le trailer après 6 secondes
    trailerTimeoutRef.current = setTimeout(() => {
      setIsTrailerPlaying(true);
      if (videoRef.current) {
        videoRef.current.play().catch(error => {
          console.log('Auto-play was prevented:', error);
        });
      }
    }, 6000); // 6 secondes

    // Nettoyer le timer si le composant est démonté ou si l'utilisateur interagit
    return () => {
      if (trailerTimeoutRef.current) {
        clearTimeout(trailerTimeoutRef.current);
        trailerTimeoutRef.current = null;
      }
    };
  }, [media?.link]);


  const handlePlay = () => {
    navigate('/player', { state: { media, type: 'movie' } });
    console.log('Playing media:', media);
  };

  const handleResume = () => {
    if (resumePosition !== null) {
      navigate('/player', {
        state: {
          media,
          type: 'movie',
          startPosition: resumePosition
        }
      });
      console.log('Resuming media from:', formatTime(resumePosition));
    } else {
      handlePlay();
    }
  };

  const handlePlayTrailer = () => {
    if (media?.trailer) {
      navigate('/player', { state: { media: { ...media, link: media.trailer }, type: 'movie' } });
      console.log('Playing trailer:', media.trailer);
    } else {
      console.log('No trailer available');
    }
  };


  // Ajouter un gestionnaire d'événements clavier global
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Enter") {
        if (selectedButton === 'play') {
          handlePlay();
        } else if (selectedButton === 'resume') {
          handleResume();
        } else if (selectedButton === 'trailer') {
          handlePlayTrailer();
        }
      } else if (e.key === RETURN_KEY || e.keyCode === RETURN_KEY_CODE) {
        // Retourner à la page précédente avec l'état de navigation
        if (navigationState) {
          navigate('/vod', {
            state: {
              restoreNavigation: true,
              ...navigationState
            }
          });
        } else {
          // Fallback si l'état de navigation n'est pas disponible
          navigate(-1);
        }
      } else if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
        // Navigation entre les boutons
        if (hasResumePoint) {
          // Si le point de reprise existe, naviguer entre les 3 boutons
          if (e.key === "ArrowRight") {
            setSelectedButton(prev => {
              if (prev === 'play') return 'resume';
              if (prev === 'resume') return 'trailer';
              return 'play';
            });
          } else {
            setSelectedButton(prev => {
              if (prev === 'trailer') return 'resume';
              if (prev === 'resume') return 'play';
              return 'trailer';
            });
          }
        } else {
          // Sans point de reprise, basculer entre play et trailer
          setSelectedButton(prev => prev === 'play' ? 'trailer' : 'play');
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [navigate, selectedButton, hasResumePoint, resumePosition, navigationState]);

  if (!media) return <div className="notFoundEpg">{t("movieNotFound")}</div>;
  return (
    <>
      {/* Votre contenu existant */}
      <div className="flex w-full h-screen" >
        {media.link && (
          <video
            ref={videoRef}
            className={`w-full h-full object-cover opacity-40 ${isTrailerPlaying ? 'block' : 'hidden'}`}
            style={{ position: 'absolute', top: 0, left: 0 }}
            loop
            playsInline
            preload="metadata"
            onLoadedData={() => {
              // Assurer que la vidéo est prête sans loading visible
              if (videoRef.current) {
                videoRef.current.currentTime = 0;
              }
            }}
          >
            <source src={media.link} type="video/mp4" />
          </video>
        )}

        <div className="relative w-3/4 h-full icon-channel2">
          {/* Image de fond statique */}
          {media.back && !isTrailerPlaying && (
            <img
              src={media.back}
              alt={media.name}
              className="w-full h-full object-cover opacity-40"
              style={{ position: 'absolute', top: 0, left: 0 }}
            />
          )}

          <div className="absolute inset-0 flex flex-col p-12 text-white z-10">
            <h1 className="text-8xl font-bold text-red-500 mb-12">{media.name}</h1>

            {(media.rate || media.date) && (
              <div className="flex items-center gap-8 mb-24">
                {/* Affichage des étoiles */}
                {media.rate && (
                  <div className="flex items-center">
                    {/* Convertit la note sur 10 en note sur 5 */}
                    {Array.from({ length: 5 }).map((_, i) => (
                      <span
                        key={i}
                        style={{
                          color: i < Math.round(Number(media.rate) / 2) ? '#FFD700' : '#888',
                          fontSize: '2rem'
                        }}
                      >
                        ★
                      </span>
                    ))}
                    <span className="ml-4 text-3xl font-bold text-yellow-400">{media.rate} / 10</span>
                  </div>
                )}
                {/* Affichage de la date */}
                {media.date && (
                  <div className="flex items-center text-2xl text-gray-200">
                    <span className="material-icons mr-2" style={{ fontSize: '2rem' }}></span>
                    {media.date}
                  </div>
                )}
              </div>
            )}

            {media.category && (
              <div className="mb-7 items-center gap-8 flex">
                <h2 className="text-3xl font-bold">{t("genre")}</h2>
                <p className="text-2xl">{media.category}</p>
              </div>
            )}

            {media.duration && (
              <div className="mb-7 items-center gap-8 flex">
                <h2 className="text-3xl font-bold">{t("duration")}</h2>
                <p className="text-2xl">{media.duration}</p>
              </div>
            )}

            {media.date_aj && (
              <div className="mb-7 items-center gap-8 flex">
                <h2 className="text-3xl font-bold">{t("added")}</h2>
                <p className="text-2xl">{media.date}</p>
              </div>
            )}

            {(media.language || media.sub) && (
              <div className="flex items-center gap-8 mb-7">
                {media.language && (
                  <div className="flex items-center">
                    <h2 className="text-3xl font-bold mr-5">{t("movieLanguage")}</h2>
                    {media.language.includes('Italien') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇮🇹'}</span>}
                    {media.language.includes('Français') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇫🇷'}</span>}
                    {media.language.includes('Anglais') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇬🇧'}</span>}
                    {media.language.includes('Espagnol') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇪🇸'}</span>}
                    {media.language.includes('Arabe') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇸🇦'}</span>}
                    {media.language.includes('Turc') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇹🇷'}</span>}
                    {media.language.includes('Hindi') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇮🇳'}</span>}
                    {media.language.includes('Allemand') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇩🇪'}</span>}
                    {media.language.includes('Coréen') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇰🇷'}</span>}
                    {media.language.includes('Chinois') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇨🇳'}</span>}
                    {media.language.includes('Japonais') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇯🇵'}</span>}
                    {media.language.includes('Russe') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇷🇺'}</span>}
                  </div>
                )}
                {media.sub && (
                  <div className="flex items-center">
                    <h2 className="text-3xl font-bold mr-5">{t("subtitle")}</h2>
                    {media.sub.includes('Français') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇫🇷'}</span>}
                    {media.sub.includes('Anglais') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇬🇧'}</span>}
                    {media.sub.includes('Espagnol') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇪🇸'}</span>}
                    {media.sub.includes('Italien') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇮🇹'}</span>}
                    {media.sub.includes('Arabe') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇸🇦'}</span>}
                    {media.sub.includes('Turc') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇹🇷'}</span>}
                    {media.sub.includes('Hindi') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇮🇳'}</span>}
                    {media.sub.includes('Allemand') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇩🇪'}</span>}
                    {media.sub.includes('Coréen') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇰🇷'}</span>}
                    {media.sub.includes('Chinois') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇨🇳'}</span>}
                    {media.sub.includes('Japonais') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇯🇵'}</span>}
                    {media.sub.includes('Russe') && <span style={{ fontSize: '2.5rem', lineHeight: 1 }}>{'🇷🇺'}</span>}
                  </div>
                )}
              </div>
            )}
            {media.actors && (
              <div className="mb-9 items-center gap-8 flex">
                <h2 className="text-3xl font-bold">{t("actors")}</h2>
                <p className="text-2xl">{media.actors}</p>
              </div>
            )}
            {media.desc && (
              <div className="mb-6 items-center gap-8 flex">
                <p className="text-2xl pr-48">{media.desc}</p>
              </div>
            )}
          </div>
        </div>
        {media.logo_desc && !isTrailerPlaying && (
          <div className="h-full flex bg-black z-10 icon-channel">
            <img
              src={media.logo_desc}
              alt={media.name}
              className="h-full w-full object-fill block"
            />
          </div>
        )}
      </div>

      <div className="flex space-x-6 absolute bottom-20 left-10">
        <div
          className={`play-icon-epg focusable ${selectedButton === 'play' ? 'active-btn' : ''}`}
          tabIndex={0}
          onClick={handlePlay}
        >
          <div className="flex items-center">
            <img
              className="w-12 h-12"
              src="/play.svg"
              alt="play"
              title="play"
            />
            <span className="trailer-text ml-2">{t("play")}</span>
          </div>
        </div>

        {hasResumePoint && (
          <div
            className={`play-icon-epg focusable ${selectedButton === 'resume' ? 'active-btn' : ''}`}
            tabIndex={0}
            onClick={handleResume}
          >
            <div className="flex items-center">
              <img
                className="w-12 h-12"
                src="/resume.svg"
                alt="resume"
                title="resume"
              />
              <span className="trailer-text ml-2">{t("resume")} ({formatTime(resumePosition || 0)})</span>
            </div>
          </div>
        )}

        <div
          className={`play-icon-epg focusable ${selectedButton === 'trailer' ? 'active-btn' : ''}`}
          tabIndex={0}
          onClick={handlePlayTrailer}
        >
          <div className="flex items-center">
            <img
              className="w-12 h-12"
              src="/play.svg"
              alt="trailer"
              title="play trailer"
            />
            <span className="trailer-text ml-2">{t("trailer")}</span>
          </div>
        </div>
      </div>

    </>
  );
};

export default MovieDescription;
