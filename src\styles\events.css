/* Styles pour les événements avec effet glass */

/* Scrollbar personnalisée pour le modal */
.events-modal-scroll::-webkit-scrollbar {
  width: 8px;
}

.events-modal-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.events-modal-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.events-modal-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animation pour les cartes d'événements */
.event-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.event-card:hover {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.event-card.selected {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 25px 50px -12px rgba(239, 68, 68, 0.25);
}

/* Animation pour les chaînes */
.channel-card {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.channel-card:hover {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.channel-card.selected {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 0 20px 40px -12px rgba(239, 68, 68, 0.3);
}

/* Effet de pulsation pour les indicateurs */
@keyframes pulse-red {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.pulse-red {
  animation: pulse-red 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Effet de brillance pour les éléments sélectionnés */
.glass-shine {
  position: relative;
  overflow: hidden;
}

.glass-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.glass-shine:hover::before {
  left: 100%;
}

/* Amélioration des ombres pour la profondeur */
.tv-shadow {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.tv-shadow-selected {
  box-shadow: 
    0 25px 50px -12px rgba(239, 68, 68, 0.25),
    0 0 0 2px rgba(239, 68, 68, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Effet de focus pour l'accessibilité */
.focus-ring {
  outline: 2px solid rgba(239, 68, 68, 0.5);
  outline-offset: 2px;
}

/* Animation d'entrée pour le modal */
@keyframes modal-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-enter {
  animation: modal-enter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Amélioration du contraste pour la lisibilité TV */
.tv-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.tv-text-bright {
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 0 8px rgba(255, 255, 255, 0.1);
}
