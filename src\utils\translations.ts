export const translations = {
  fr: {
    // General
    welcome: "Bienvenue",
    // Navigation
    multipacks: "Multipacks",
    settings: "Paramètres",
    
    // Settings page
    reset: "Reset",
    profile: "Profil",
    language: "Langue",
    about: "À propos",
    
    // Language page
    chooseLanguage: "Choisir la langue",
    
    // About page
    version: "Version",
    mediaStreamingApp: "Application de streaming multimédia",
    developedFor: "Développée pour Samsung Smart TV",
    features: "Fonctionnalités:",
    featuresRadio: "Radio en direct",
    featuresQuran: "Lecture du Coran",
    
    // Media types
    vod: "Films",
    series: "Séries",
    radio: "Radio",
    coran: "Coran",
    live: "Direct",
    event: "Événements",
    
    // Special categories
    favorites: "★ Favoris",
    stillWatching: "👁 En cours",
    
    // Login page
    login: "Connexion",
    loginWithCode: "Connexion avec votre code SMART+",
    loginWithDefaultCode: "Connexion avec le code par défaut 1234567890",
    inputYourCode: "Saisissez votre code",
    ok: "OK",
    loggingIn: "Connexion...",
    pleaseInputCode: "Veuillez saisir votre code",
    loginSuccessful: "Connexion réussie: ",
    invalidCode: "Code invalide",
    loginFailed: "Échec de la connexion. Veuillez réessayer.",
    
    // Movie detail page
    movieNotFound: "Film introuvable",
    genre: "Genre:",
    duration: "Durée:",
    added: "Ajouté:",
    movieLanguage: "Langue:",
    subtitle: "Sous-titres:",
    actors: "Acteurs:",
    play: "Lire",
    resume: "Reprendre",
    trailer: "Bande-annonce",
    
    // Series detail page
    seriesNotFound: "Série introuvable",
    seasons: "Saisons",
    episodes: "Épisodes",
    noEpisodeAvailable: "Aucun épisode disponible",
    noDescriptionAvailable: "Aucune description disponible",
    resumeVideoConfirm: "Voulez-vous reprendre la vidéo à {time} ?\n\nAppuyez sur OK pour reprendre ou Annuler pour commencer depuis le début.",
    
    // Media page
    tryAgain: "Réessayer",
    noFavoritesYet: "Aucun favori pour le moment",
    noItemsInProgress: "Aucun élément en cours",
    noResultsFound: "Aucun résultat trouvé pour \"{query}\"",
    addToFavorites: "Voulez-vous ajouter \"{name}\" aux favoris ?",
    removeFromFavorites: "Voulez-vous retirer \"{name}\" des favoris ?",
    addCategoryToFavorites: "Voulez-vous ajouter la catégorie \"{name}\" aux favoris ?",
    removeCategoryFromFavorites: "Voulez-vous retirer la catégorie \"{name}\" des favoris ?",
    addedToFavorites: "\"{name}\" a été ajouté aux favoris !",
    removedFromFavorites: "\"{name}\" a été retiré des favoris !",
    alreadyInFavorites: "\"{name}\" est déjà dans vos favoris.",
    categoryAddedToFavorites: "La catégorie \"{name}\" a été ajoutée aux favoris !",
    categoryRemovedFromFavorites: "La catégorie \"{name}\" a été retirée des favoris !",
    logoutConfirm: "Voulez-vous vous déconnecter de votre compte ?",
  },
  
  en: {
    // General
    welcome: "Welcome",
    
    // Navigation
    multipacks: "Multipacks",
    settings: "Settings",
    
    // Settings page
    reset: "Reset",
    profile: "Profile",
    language: "Language",
    about: "About",
    
    // Language page
    chooseLanguage: "Choose Language",
    
    // About page
    version: "Version",
    mediaStreamingApp: "Media streaming application",
    developedFor: "Developed for Samsung Smart TV",
    features: "Features:",
    featuresRadio: "Live radio",
    featuresQuran: "Quran recitation",
    
    // Media types
    vod: "Movies",
    series: "Series",
    radio: "Radio",
    coran: "Quran",
    live: "Live",
    event: "Events",
    
    // Special categories
    favorites: "★ Favorites",
    stillWatching: "👁 Still Watching",
    
    // Login page
    login: "Login",
    loginWithCode: "Login with your SMART+ code",
    loginWithDefaultCode: "Login with default code 1234567890",
    inputYourCode: "Input Your Code",
    ok: "OK",
    loggingIn: "Logging in...",
    pleaseInputCode: "Please input your code",
    loginSuccessful: "Login successful: ",
    invalidCode: "Invalid code",
    loginFailed: "Login failed. Please try again.",
    
    // Movie detail page
    movieNotFound: "Movie not found",
    genre: "Genre:",
    duration: "Duration:",
    added: "Added:",
    movieLanguage: "Language:",
    subtitle: "Subtitle:",
    actors: "Actors:",
    play: "Play",
    resume: "Resume",
    trailer: "Trailer",
    
    // Series detail page
    seriesNotFound: "Series not found",
    seasons: "Seasons",
    episodes: "Episodes",
    noEpisodeAvailable: "No episode available",
    noDescriptionAvailable: "No description available",
    resumeVideoConfirm: "Do you want to resume the video at {time}?\n\nPress OK to resume or Cancel to start from the beginning.",
    
    // Media page
    tryAgain: "Try Again",
    noFavoritesYet: "No favorites yet",
    noItemsInProgress: "No items in progress",
    noResultsFound: "No results found for \"{query}\"",
    addToFavorites: "Do you want to add \"{name}\" to favorites?",
    removeFromFavorites: "Do you want to remove \"{name}\" from favorites?",
    addCategoryToFavorites: "Do you want to add the category \"{name}\" to favorites?",
    removeCategoryFromFavorites: "Do you want to remove the category \"{name}\" from favorites?",
    addedToFavorites: "\"{name}\" has been added to favorites!",
    removedFromFavorites: "\"{name}\" has been removed from favorites!",
    alreadyInFavorites: "\"{name}\" is already in your favorites.",
    categoryAddedToFavorites: "The category \"{name}\" has been added to favorites!",
    categoryRemovedFromFavorites: "The category \"{name}\" has been removed from favorites!",
    logoutConfirm: "Do you want to logout from your account?",
  },
  
  ar: {
    // General
    welcome: "مرحباً",

    // Navigation
    multipacks: "الحزم المتعددة",
    settings: "الإعدادات",
    
    // Settings page
    reset: "إعادة تعيين",
    profile: "الملف الشخصي",
    language: "اللغة",
    about: "حول التطبيق",
    
    // Language page
    chooseLanguage: "اختر اللغة",
    
    // About page
    version: "الإصدار",
    mediaStreamingApp: "تطبيق بث الوسائط",
    developedFor: "مطور لتلفزيون سامسونغ الذكي",
    features: "المميزات:",
    featuresRadio: "راديو مباشر",
    featuresQuran: "تلاوة القرآن",
    
    // Media types
    vod: "أفلام",
    series: "مسلسلات",
    radio: "راديو",
    coran: "القرآن",
    live: "مباشر",
    event: "فعاليات",
    
    // Special categories
    favorites: "★ المفضلة",
    stillWatching: "👁 قيد المشاهدة",
    
    // Login page
    login: "تسجيل الدخول",
    loginWithCode: "تسجيل الدخول باستخدام رمز SMART+ الخاص بك",
    loginWithDefaultCode: "تسجيل الدخول باستخدام الرمز الافتراضي 1234567890",
    inputYourCode: "أدخل رمزك",
    ok: "موافق",
    loggingIn: "جاري تسجيل الدخول...",
    pleaseInputCode: "يرجى إدخال رمزك",
    loginSuccessful: "تم تسجيل الدخول بنجاح: ",
    invalidCode: "رمز غير صالح",
    loginFailed: "فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.",
    
    // Movie detail page
    movieNotFound: "الفيلم غير موجود",
    genre: "النوع:",
    duration: "المدة:",
    added: "تم الإضافة:",
    movieLanguage: "اللغة:",
    subtitle: "الترجمة:",
    actors: "الممثلون:",
    play: "تشغيل",
    resume: "استئناف",
    trailer: "المقطع الدعائي",
    
    // Series detail page
    seriesNotFound: "المسلسل غير موجود",
    seasons: "المواسم",
    episodes: "الحلقات",
    noEpisodeAvailable: "لا توجد حلقة متاحة",
    noDescriptionAvailable: "لا يوجد وصف متاح",
    resumeVideoConfirm: "هل تريد استئناف الفيديو من {time}؟\n\nاضغط موافق للاستئناف أو إلغاء للبدء من البداية.",
    
    // Media page
    tryAgain: "المحاولة مرة أخرى",
    noFavoritesYet: "لا توجد مفضلات بعد",
    noItemsInProgress: "لا توجد عناصر قيد التقدم",
    noResultsFound: "لم يتم العثور على نتائج لـ \"{query}\"",
    addToFavorites: "هل تريد إضافة \"{name}\" إلى المفضلة؟",
    removeFromFavorites: "هل تريد إزالة \"{name}\" من المفضلة؟",
    addCategoryToFavorites: "هل تريد إضافة الفئة \"{name}\" إلى المفضلة؟",
    removeCategoryFromFavorites: "هل تريد إزالة الفئة \"{name}\" من المفضلة؟",
    addedToFavorites: "تم إضافة \"{name}\" إلى المفضلة!",
    removedFromFavorites: "تم إزالة \"{name}\" من المفضلة!",
    alreadyInFavorites: "\"{name}\" موجود بالفعل في مفضلاتك.",
    categoryAddedToFavorites: "تم إضافة الفئة \"{name}\" إلى المفضلة!",
    categoryRemovedFromFavorites: "تم إزالة الفئة \"{name}\" من المفضلة!",
    logoutConfirm: "هل تريد تسجيل الخروج من حسابك؟",
  },
  
  it: {
    // General
    welcome: "Benvenuto",

    // Navigation
    multipacks: "Multipacks",
    settings: "Impostazioni",
    
    // Settings page
    reset: "Reset",
    profile: "Profilo",
    language: "Lingua",
    about: "Informazioni",
    
    // Language page
    chooseLanguage: "Scegli Lingua",
    
    // About page
    version: "Versione",
    mediaStreamingApp: "Applicazione di streaming multimediale",
    developedFor: "Sviluppata per Samsung Smart TV",
    features: "Caratteristiche:",
    featuresRadio: "Radio dal vivo",
    featuresQuran: "Recitazione del Corano",
    
    // Media types
    vod: "Film",
    series: "Serie",
    radio: "Radio",
    coran: "Corano",
    live: "Dal vivo",
    event: "Eventi",
    
    // Special categories
    favorites: "★ Preferiti",
    stillWatching: "👁 In visione",
    
    // Login page
    login: "Accesso",
    loginWithCode: "Accedi con il tuo codice SMART+",
    loginWithDefaultCode: "Accedi con il codice predefinito 1234567890",
    inputYourCode: "Inserisci il tuo codice",
    ok: "OK",
    loggingIn: "Accesso in corso...",
    pleaseInputCode: "Inserisci il tuo codice",
    loginSuccessful: "Accesso riuscito: ",
    invalidCode: "Codice non valido",
    loginFailed: "Accesso fallito. Riprova.",
    
    // Movie detail page
    movieNotFound: "Film non trovato",
    genre: "Genere:",
    duration: "Durata:",
    added: "Aggiunto:",
    movieLanguage: "Lingua:",
    subtitle: "Sottotitoli:",
    actors: "Attori:",
    play: "Riproduci",
    resume: "Riprendi",
    trailer: "Trailer",
    
    // Series detail page
    seriesNotFound: "Serie non trovata",
    seasons: "Stagioni",
    episodes: "Episodi",
    noEpisodeAvailable: "Nessun episodio disponibile",
    noDescriptionAvailable: "Nessuna descrizione disponibile",
    resumeVideoConfirm: "Vuoi riprendere il video da {time}?\n\nPremi OK per riprendere o Annulla per iniziare dall'inizio.",
    
    // Media page
    tryAgain: "Riprova",
    noFavoritesYet: "Nessun preferito ancora",
    noItemsInProgress: "Nessun elemento in corso",
    noResultsFound: "Nessun risultato trovato per \"{query}\"",
    addToFavorites: "Vuoi aggiungere \"{name}\" ai preferiti?",
    removeFromFavorites: "Vuoi rimuovere \"{name}\" dai preferiti?",
    addCategoryToFavorites: "Vuoi aggiungere la categoria \"{name}\" ai preferiti?",
    removeCategoryFromFavorites: "Vuoi rimuovere la categoria \"{name}\" dai preferiti?",
    addedToFavorites: "\"{name}\" è stato aggiunto ai preferiti!",
    removedFromFavorites: "\"{name}\" è stato rimosso dai preferiti!",
    alreadyInFavorites: "\"{name}\" è già nei tuoi preferiti.",
    categoryAddedToFavorites: "La categoria \"{name}\" è stata aggiunta ai preferiti!",
    categoryRemovedFromFavorites: "La categoria \"{name}\" è stata rimossa dai preferiti!",
    logoutConfirm: "Vuoi disconnetterti dal tuo account?",
  },
  
  es: {
    // General
    welcome: "Bienvenido",

    // Navigation
    multipacks: "Multipacks",
    settings: "Configuración",
    
    // Settings page
    reset: "Restablecer",
    profile: "Perfil",
    language: "Idioma",
    about: "Acerca de",
    
    // Language page
    chooseLanguage: "Elegir Idioma",
    
    // About page
    version: "Versión",
    mediaStreamingApp: "Aplicación de streaming multimedia",
    developedFor: "Desarrollada para Samsung Smart TV",
    features: "Características:",
    featuresRadio: "Radio en vivo",
    featuresQuran: "Recitación del Corán",
    
    // Media types
    vod: "Películas",
    series: "Series",
    radio: "Radio",
    coran: "Corán",
    live: "En vivo",
    event: "Eventos",
    
    // Special categories
    favorites: "★ Favoritos",
    stillWatching: "👁 Viendo",
    
    // Login page
    login: "Iniciar sesión",
    loginWithCode: "Inicia sesión con tu código SMART+",
    loginWithDefaultCode: "Inicia sesión con el código predeterminado 1234567890",
    inputYourCode: "Ingresa tu código",
    ok: "OK",
    loggingIn: "Iniciando sesión...",
    pleaseInputCode: "Por favor ingresa tu código",
    loginSuccessful: "Inicio de sesión exitoso: ",
    invalidCode: "Código inválido",
    loginFailed: "Error al iniciar sesión. Intenta de nuevo.",
    
    // Movie detail page
    movieNotFound: "Película no encontrada",
    genre: "Género:",
    duration: "Duración:",
    added: "Agregado:",
    movieLanguage: "Idioma:",
    subtitle: "Subtítulos:",
    actors: "Actores:",
    play: "Reproducir",
    resume: "Reanudar",
    trailer: "Tráiler",
    
    // Series detail page
    seriesNotFound: "Serie no encontrada",
    seasons: "Temporadas",
    episodes: "Episodios",
    noEpisodeAvailable: "No hay episodios disponibles",
    noDescriptionAvailable: "No hay descripción disponible",
    resumeVideoConfirm: "¿Quieres reanudar el video en {time}?\n\nPresiona OK para reanudar o Cancelar para empezar desde el principio.",
    
    // Media page
    tryAgain: "Intentar de nuevo",
    noFavoritesYet: "No hay favoritos aún",
    noItemsInProgress: "No hay elementos en progreso",
    noResultsFound: "No se encontraron resultados para \"{query}\"",
    addToFavorites: "¿Quieres agregar \"{name}\" a favoritos?",
    removeFromFavorites: "¿Quieres quitar \"{name}\" de favoritos?",
    addCategoryToFavorites: "¿Quieres agregar la categoría \"{name}\" a favoritos?",
    removeCategoryFromFavorites: "¿Quieres quitar la categoría \"{name}\" de favoritos?",
    addedToFavorites: "¡\"{name}\" ha sido agregado a favoritos!",
    removedFromFavorites: "¡\"{name}\" ha sido quitado de favoritos!",
    alreadyInFavorites: "\"{name}\" ya está en tus favoritos.",
    categoryAddedToFavorites: "¡La categoría \"{name}\" ha sido agregada a favoritos!",
    categoryRemovedFromFavorites: "¡La categoría \"{name}\" ha sido quitada de favoritos!",
    logoutConfirm: "¿Quieres desconectarte de tu cuenta?",
  },
  
  de: {
    // General
    welcome: "Willkommen",

    // Navigation
    multipacks: "Multipacks",
    settings: "Einstellungen",
    
    // Settings page
    reset: "Zurücksetzen",
    profile: "Profil",
    language: "Sprache",
    about: "Über",
    
    // Language page
    chooseLanguage: "Sprache wählen",
    
    // About page
    version: "Version",
    mediaStreamingApp: "Media-Streaming-Anwendung",
    developedFor: "Entwickelt für Samsung Smart TV",
    features: "Funktionen:",
    featuresRadio: "Live-Radio",
    featuresQuran: "Koran-Rezitation",
    
    // Media types
    vod: "Filme",
    series: "Serien",
    radio: "Radio",
    coran: "Koran",
    live: "Live",
    event: "Veranstaltungen",
    
    // Special categories
    favorites: "★ Favoriten",
    stillWatching: "👁 Gerade angesehen",
    
    // Login page
    login: "Anmelden",
    loginWithCode: "Mit Ihrem SMART+ Code anmelden",
    loginWithDefaultCode: "Mit Standardcode 1234567890 anmelden",
    inputYourCode: "Geben Sie Ihren Code ein",
    ok: "OK",
    loggingIn: "Anmeldung läuft...",
    pleaseInputCode: "Bitte geben Sie Ihren Code ein",
    loginSuccessful: "Anmeldung erfolgreich: ",
    invalidCode: "Ungültiger Code",
    loginFailed: "Anmeldung fehlgeschlagen. Bitte versuchen Sie es erneut.",
    
    // Movie detail page
    movieNotFound: "Film nicht gefunden",
    genre: "Genre:",
    duration: "Dauer:",
    added: "Hinzugefügt:",
    movieLanguage: "Sprache:",
    subtitle: "Untertitel:",
    actors: "Schauspieler:",
    play: "Abspielen",
    resume: "Fortsetzen",
    trailer: "Trailer",
    
    // Series detail page
    seriesNotFound: "Serie nicht gefunden",
    seasons: "Staffeln",
    episodes: "Episoden",
    noEpisodeAvailable: "Keine Episode verfügbar",
    noDescriptionAvailable: "Keine Beschreibung verfügbar",
    resumeVideoConfirm: "Möchten Sie das Video bei {time} fortsetzen?\n\nDrücken Sie OK zum Fortsetzen oder Abbrechen, um von vorne zu beginnen.",
    
    // Media page
    tryAgain: "Erneut versuchen",
    noFavoritesYet: "Noch keine Favoriten",
    noItemsInProgress: "Keine Elemente in Bearbeitung",
    noResultsFound: "Keine Ergebnisse für \"{query}\" gefunden",
    addToFavorites: "Möchten Sie \"{name}\" zu den Favoriten hinzufügen?",
    removeFromFavorites: "Möchten Sie \"{name}\" aus den Favoriten entfernen?",
    addCategoryToFavorites: "Möchten Sie die Kategorie \"{name}\" zu den Favoriten hinzufügen?",
    removeCategoryFromFavorites: "Möchten Sie die Kategorie \"{name}\" aus den Favoriten entfernen?",
    addedToFavorites: "\"{name}\" wurde zu den Favoriten hinzugefügt!",
    removedFromFavorites: "\"{name}\" wurde aus den Favoriten entfernt!",
    alreadyInFavorites: "\"{name}\" ist bereits in Ihren Favoriten.",
    categoryAddedToFavorites: "Die Kategorie \"{name}\" wurde zu den Favoriten hinzugefügt!",
    categoryRemovedFromFavorites: "Die Kategorie \"{name}\" wurde aus den Favoriten entfernt!",
    logoutConfirm: "Möchten Sie sich von Ihrem Konto abmelden?",
  }
};

export type TranslationKey = keyof typeof translations.fr;
