import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { EventChannel } from '../types/events';
import { useTranslation } from '../hooks/useTranslation';
import { RETURN_KEY_CODE } from '../utils/keysCode';

interface EventChannelsModalProps {
  eventId: string;
  eventName: string;
  isOpen: boolean;
  onClose: () => void;
}

const EventChannelsModal: React.FC<EventChannelsModalProps> = ({
  eventId,
  eventName,
  isOpen,
  onClose
}) => {
  const [channels, setChannels] = useState<EventChannel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);
  const channelsContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && eventId) {
      fetchEventChannels();
    }
  }, [isOpen, eventId]);

  const fetchEventChannels = async () => {
    try {
      setLoading(true);
      setError(null);
      setSelectedIndex(0);
      
      const response = await api.fetchEventsChannels(eventId);
      
      if (typeof response === 'string') {
        setError(response);
        return;
      }

      if (response && response.data && response.data.channels) {
        setChannels(response.data.channels);
      } else {
        setError('No channels data received');
      }
    } catch (err) {
      setError('Failed to load event channels. Please try again later.');
      console.error('Error fetching event channels:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isOpen) return;

    // Empêcher la propagation vers les composants parents
    event.stopPropagation();

    switch (event.keyCode) {
      case 38: // Up arrow
        event.preventDefault();
        if (channels.length > 0) {
          setSelectedIndex(prev => Math.max(0, prev - 1));
        }
        break;
      case 40: // Down arrow
        event.preventDefault();
        if (channels.length > 0) {
          setSelectedIndex(prev => Math.min(channels.length - 1, prev + 1));
        }
        break;
      case 13: // Enter
        event.preventDefault();
        if (channels.length > 0 && !loading) {
          handleChannelSelect(channels[selectedIndex]);
        }
        break;
      case RETURN_KEY_CODE:
      case 8: // Backspace
      case 27: // Escape
        event.preventDefault();
        onClose();
        break;
    }
  };

  const handleChannelSelect = (channel: EventChannel) => {
    // Naviguer vers le player avec le lien de la chaîne
    navigate('/player', {
      state: {
        mediaUrl: channel.link,
        mediaTitle: channel.name,
        mediaLogo: channel.logo,
        mediaType: 'live',
        eventName: eventName
      }
    });
  };

  useEffect(() => {
    if (isOpen) {
      // Utiliser capture: true pour intercepter les événements avant les autres composants
      window.addEventListener('keydown', handleKeyDown, true);
      return () => {
        window.removeEventListener('keydown', handleKeyDown, true);
      };
    }
  }, [isOpen, channels, selectedIndex, loading]);

  // Auto-scroll to selected item
  useEffect(() => {
    if (channelsContainerRef.current && channels.length > 0) {
      const selectedElement = channelsContainerRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  }, [selectedIndex]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div 
        ref={modalRef}
        className="bg-gray-900 rounded-lg p-8 max-w-4xl w-full max-h-[80vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white">{eventName}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-2xl text-white">{t('loading')}</div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-xl text-red-500 mb-4">{error}</div>
              <button 
                onClick={fetchEventChannels}
                className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {t('retry')}
              </button>
            </div>
          </div>
        ) : channels.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-xl text-gray-400">{t('no_channels')}</div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-lg text-gray-300 mb-4">
              {t('select_channel')} ({channels.length} {t('channels_available')})
            </div>
            <div 
              ref={channelsContainerRef}
              className="space-y-3 max-h-96 overflow-y-auto"
            >
              {channels.map((channel, index) => (
                <div
                  key={channel.id}
                  className={`flex items-center p-4 rounded-lg transition-all duration-200 cursor-pointer ${
                    selectedIndex === index
                      ? 'bg-blue-600 scale-105 shadow-lg'
                      : 'bg-gray-800 hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-4 flex-1">
                    {/* Logo de la chaîne */}
                    <img
                      src={channel.logo}
                      alt={channel.name}
                      className="w-12 h-12 object-contain rounded"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/NoLogo.png';
                      }}
                    />
                    
                    {/* Nom de la chaîne */}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white">{channel.name}</h3>
                    </div>
                  </div>
                  
                  {/* Indicateur de sélection */}
                  {selectedIndex === index && (
                    <div className="ml-4">
                      <i className="fas fa-play text-white text-lg"></i>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 text-center text-gray-400 text-sm">
          {t('press_enter_to_play')} • {t('press_back_to_close')}
        </div>
      </div>
    </div>
  );
};

export default EventChannelsModal;
