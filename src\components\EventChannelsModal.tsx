import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { EventChannel } from '../types/events';
import { useTranslation } from '../hooks/useTranslation';
import { RETURN_KEY_CODE } from '../utils/keysCode';
import '../styles/events.css';

interface EventChannelsModalProps {
  eventId: string;
  eventName: string;
  isOpen: boolean;
  onClose: () => void;
}

const EventChannelsModal: React.FC<EventChannelsModalProps> = ({
  eventId,
  eventName,
  isOpen,
  onClose
}) => {
  const [channels, setChannels] = useState<EventChannel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);
  const channelsContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && eventId) {
      fetchEventChannels();
    }
  }, [isOpen, eventId]);

  const fetchEventChannels = async () => {
    try {
      setLoading(true);
      setError(null);
      setSelectedIndex(0);

      const response = await api.fetchEventsChannels(eventId);

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      if (response && response.data && response.data.channels) {
        setChannels(response.data.channels);
      } else {
        setError('No channels data received');
      }
    } catch (err) {
      setError('Failed to load event channels. Please try again later.');
      console.error('Error fetching event channels:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isOpen) return;

    // Empêcher la propagation vers les composants parents
    event.stopPropagation();

    switch (event.keyCode) {
      case 38: // Up arrow
        event.preventDefault();
        if (channels.length > 0) {
          setSelectedIndex(prev => Math.max(0, prev - 1));
        }
        break;
      case 40: // Down arrow
        event.preventDefault();
        if (channels.length > 0) {
          setSelectedIndex(prev => Math.min(channels.length - 1, prev + 1));
        }
        break;
      case 13: // Enter
        event.preventDefault();
        if (channels.length > 0 && !loading) {
          handleChannelSelect(channels[selectedIndex]);
        }
        break;
      case RETURN_KEY_CODE:
      case 8: // Backspace
      case 27: // Escape
        event.preventDefault();
        onClose();
        break;
    }
  };

  const handleChannelSelect = (channel: EventChannel) => {
    // Naviguer vers le player avec le lien de la chaîne
    navigate('/player', {
      state: {
        mediaUrl: channel.link,
        mediaTitle: channel.name,
        mediaLogo: channel.logo,
        mediaType: 'live',
        eventName: eventName
      }
    });
  };

  useEffect(() => {
    if (isOpen) {
      // Utiliser capture: true pour intercepter les événements avant les autres composants
      window.addEventListener('keydown', handleKeyDown, true);
      return () => {
        window.removeEventListener('keydown', handleKeyDown, true);
      };
    }
  }, [isOpen, channels, selectedIndex, loading]);

  // Auto-scroll to selected item
  useEffect(() => {
    if (channelsContainerRef.current && channels.length > 0) {
      const selectedElement = channelsContainerRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  }, [selectedIndex]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="modal-enter backdrop-blur-md bg-white/10 border-2 border-white/20 rounded-3xl p-12 max-w-6xl w-full max-h-[85vh] overflow-hidden tv-shadow"
        style={{
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05))'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-5xl font-bold text-white tv-text-bright">{eventName}</h2>
          <button
            onClick={onClose}
            className="text-gray-300 hover:text-white text-4xl transition-colors duration-200 hover:scale-110"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center h-80">
            <div className="text-4xl text-white tv-text-bright">{t('loading')}</div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-80">
            <div className="text-center">
              <div className="text-3xl text-red-400 mb-6 tv-text-bright">{error}</div>
              <button
                onClick={fetchEventChannels}
                className="px-8 py-4 bg-red-600/80 backdrop-blur-sm text-white text-xl rounded-xl hover:bg-red-700/80 transition-all duration-200 border border-red-500/50"
              >
                {t('retry')}
              </button>
            </div>
          </div>
        ) : channels.length === 0 ? (
          <div className="flex items-center justify-center h-80">
            <div className="text-3xl text-gray-300 tv-text">{t('no_channels')}</div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-2xl text-gray-200 mb-6 tv-text">
              {t('select_channel')} ({channels.length} {t('channels_available')})
            </div>
            <div
              ref={channelsContainerRef}
              className="events-modal-scroll space-y-4 max-h-[50vh] overflow-y-auto pr-4"
            >
              {channels.map((channel, index) => (
                <div
                  key={channel.id}
                  className={`channel-card tv-focus relative flex items-center p-6 rounded-2xl border-2 ${
                    selectedIndex === index
                      ? 'glass-shine selected border-red-500 tv-shadow-selected'
                      : 'border-white/20 tv-shadow'
                    }`}
                  style={{
                    background: selectedIndex === index
                      ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.3), rgba(239, 68, 68, 0.1))'
                      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05))'
                  }}
                >
                  <div className="flex items-center space-x-6 flex-1">
                    {/* Logo de la chaîne */}
                    <img
                      src={channel.logo}
                      alt={channel.name}
                      className="w-16 h-16 object-contain rounded-xl bg-white/20 p-2 drop-shadow-lg"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/NoLogo.png';
                      }}
                    />

                    {/* Nom de la chaîne */}
                    <div className="flex-1">
                      <h3 className="text-2xl font-semibold text-white tv-text-bright">{channel.name}</h3>
                    </div>
                  </div>

                  {/* Indicateur de sélection */}
                  {selectedIndex === index && (
                    <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
                      <div
                        className="flex items-center justify-center"
                        style={{
                          width: '56px',
                          height: '56px',
                          borderRadius: '50%',
                          background: 'radial-gradient(circle, #ef4444 80%, #fff0 100%)',
                          boxShadow: '0 0 16px 4px rgba(239,68,68,0.5), 0 2px 8px rgba(0,0,0,0.2)',
                          animation: 'pulse-red 1.5s infinite',
                          border: '3px solid #fff3',
                        }}
                      >
                        <i className="fas fa-play text-white text-3xl" style={{ filter: 'drop-shadow(0 0 6px #fff)' }}></i>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-8 text-center text-gray-300 text-xl tv-text">
          {t('press_enter_to_play')} • {t('press_back_to_close')}
        </div>
      </div>
    </div>
  );
};

export default EventChannelsModal;
