import { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { getCachedData } from '../utils/tizenStorage';
import { formatTime } from '../utils/formatTime';
import { Episode } from '../types/series';
import { RETURN_KEY, RETURN_KEY_CODE } from '../utils/keysCode';
import { useTranslation } from '../hooks/useTranslation';

const SeriesDescription = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const series = state?.series;
  const [seasons,] = useState(state?.seasons || []);
  const [episodes, setEpisodes] = useState(state?.episodes || []);
  const [, setCurrentSeasonId] = useState(state?.currentSeasonId);
  const [loading, setLoading] = useState(false);
  const [, setError] = useState<string | null>(null);
  const [selectedSeason, setSelectedSeason] = useState(0);
  const [activeSeason, setActiveSeason] = useState(0); // Nouvelle variable pour la saison active (celle dont les épisodes sont affichés)
  const [selectedEpisode, setSelectedEpisode] = useState(0);
  const [activeZone, setActiveZone] = useState<'seasons' | 'episodes'>('seasons');
  const [isTrailerPlaying, setIsTrailerPlaying] = useState(false);
  const trailerTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  // Référence pour le conteneur des saisons
  const seasonsContainerRef = useRef<HTMLDivElement>(null);
  // Référence pour le conteneur des épisodes
  const episodesContainerRef = useRef<HTMLDivElement>(null);
  const [episodeProgress, setEpisodeProgress] = useState<Record<string, number>>({});

  // Stocker l'état de navigation reçu de MediaPage
  const navigationState = state?.navigationState;

  // Démarrer automatiquement le trailer après 6 secondes
  useEffect(() => {
    if (!series?.name) return;

    // Démarrer le timer pour lire le trailer après 6 secondes
    trailerTimeoutRef.current = setTimeout(() => {
      setIsTrailerPlaying(true);
      if (videoRef.current) {
        videoRef.current.play().catch(error => {
          console.log('Auto-play was prevented:', error);
        });
      }
    }, 6000); // 6 secondes

    // Nettoyer le timer si le composant est démonté ou si l'utilisateur interagit
    return () => {
      if (trailerTimeoutRef.current) {
        clearTimeout(trailerTimeoutRef.current);
        trailerTimeoutRef.current = null;
      }
    };
  }, [series?.name]);

  // Arrêter le trailer automatique si l'utilisateur interagit
  const stopAutoTrailer = () => {
    if (trailerTimeoutRef.current) {
      clearTimeout(trailerTimeoutRef.current);
      trailerTimeoutRef.current = null;
    }
    if (isTrailerPlaying && videoRef.current) {
      videoRef.current.pause();
      setIsTrailerPlaying(false);
    }
  };

  // Charger les épisodes lorsqu'une saison est sélectionnée
  const loadEpisodesForSeason = async (seasonId: string, index: number) => {
    stopAutoTrailer(); // Arrêter le trailer lors de l'interaction
    try {
      setLoading(true);
      setSelectedSeason(index);
      setActiveSeason(index); // Mettre à jour la saison active
      if (!state?.restoreNavigation) {
        setSelectedEpisode(0);
      }
      const response = await api.fetchEpisodes(seasonId);

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      const formattedEpisodes = (response.data.episodes || []).map((episode: any) => ({
        id: episode.id,
        name: episode.name,
        type: episode.type,
        logo: episode.thumb,
        logo_desc: episode.logo,
        link: episode.link,
        date: episode.date,
        duration: episode.duration,
        desc: episode.desc
      }));

      setEpisodes(formattedEpisodes);
      setCurrentSeasonId(seasonId);
      setError(null);
    } catch (err) {
      setError('Failed to load episodes');
      console.error('Error fetching episodes:', err);
    } finally {
      setLoading(false);
    }
  };

  // Gérer les touches du clavier
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Arrêter le trailer automatique lors de toute interaction
      stopAutoTrailer();
      
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Enter'].includes(e.key)) {
        e.preventDefault();
      }

      if (e.key === RETURN_KEY || e.keyCode === RETURN_KEY_CODE) {
        e.preventDefault();

        // Si nous avons un état de navigation, retourner à MediaPage avec cet état
        if (navigationState) {
          console.log("Returning to MediaPage with navigation state:", navigationState);
          navigate("/series", {
            state: {
              restoreNavigation: true,
              ...navigationState
            }
          });
        } else {
          // Fallback si l'état de navigation n'est pas disponible
          navigate(-1);
        }
        return;
      }

      // Navigation entre les saisons et épisodes
      if (activeZone === 'seasons') {
        // Navigation dans la liste des saisons
        if (e.key === 'ArrowDown') {
          setSelectedSeason(prev => Math.min(prev + 1, seasons.length - 1));
        } else if (e.key === 'ArrowUp') {
          setSelectedSeason(prev => Math.max(prev - 1, 0));
        } else if (e.key === 'ArrowRight' && episodes.length > 0) {
          // Passer à la zone des épisodes
          setActiveZone('episodes');
        } else if (e.key === 'Enter') {
          // Charger les épisodes de la saison sélectionnée
          loadEpisodesForSeason(seasons[selectedSeason].id, selectedSeason);
        }
      } else if (activeZone === 'episodes') {
        // Navigation dans la liste des épisodes (verticale)
        if (e.key === 'ArrowDown') {
          // Aller à l'épisode suivant
          if (selectedEpisode < episodes.length - 1) {
            setSelectedEpisode(prev => prev + 1);
          }
        } else if (e.key === 'ArrowUp') {
          // Aller à l'épisode précédent
          if (selectedEpisode > 0) {
            setSelectedEpisode(prev => prev - 1);
          }
        } else if (e.key === 'ArrowLeft') {
          // Revenir à la liste des saisons
          setActiveZone('seasons');
        } else if (e.key === 'Enter') {
          // Lancer la lecture de l'épisode sélectionné
          handleEpisodeClick(episodes[selectedEpisode]);
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [navigate, seasons, episodes, selectedSeason, selectedEpisode, activeZone, loadEpisodesForSeason, navigationState]);

  // Lorsqu'un épisode est sélectionné pour la lecture
  const playEpisode = (episode: Episode, startPosition?: number) => {
    stopAutoTrailer(); // Arrêter le trailer lors de la navigation
    // Sauvegarder l'état actuel de navigation
    const currentNavigationState = {
      series,
      seasons,
      currentSeasonId: seasons[activeSeason]?.id,
      selectedSeason,
      selectedEpisode,
      activeZone,
      navigationState : navigationState
    };

    console.log("currentNavigationState", currentNavigationState)
    navigate("/player", {
      state: {
        media: episode,
        backlink: series.back,
        type : 'serie',
        startPosition,
        seriesNavigationState: currentNavigationState,
        episodes: state.episodes
      }, 

    } 
  );
  console.log("state",state)
  };

  const handleEpisodeClick = async (episode: Episode) => {
    stopAutoTrailer(); // Arrêter le trailer lors de la sélection d'épisode
    try {
      const savedPosition = await getCachedData<{
        position: number;
        timestamp: number;
        duration: number;
      }>(`movie_position_${episode.link}`);

      console.log("savedPostion in handle", savedPosition)

      // Vérifier si la position est valide et significative
      const isValidPosition = savedPosition &&
        savedPosition.position > 30000 && // Plus de 30 secondes
        savedPosition.duration > 0
      console.log("isvalidPostion", isValidPosition)
      if (isValidPosition) {
        // Afficher une modal de confirmation
        const confirmMessage = t("resumeVideoConfirm").replace("{time}", formatTime(savedPosition.position));
        const shouldResume = window.confirm(confirmMessage);

        if (shouldResume) {
          playEpisode(episode, savedPosition.position);
        } else {
          playEpisode(episode);
        }
      } else {
        playEpisode(episode);
      }
    } catch (error) {
      console.error('Error checking resume position:', error);
      playEpisode(episode);
    }
  };

  useEffect(() => {
    if (state?.restoreNavigation) {
      // Restaurer l'état de navigation
      setSelectedSeason(state.selectedSeason);
      setActiveSeason(state.seasons.findIndex((s: { id: any; }) => s.id === state.currentSeasonId));
      setSelectedEpisode(state.selectedEpisode);
      setActiveZone(state.activeZone);

      // Recharger les épisodes de la saison active
      if (state.currentSeasonId) {
        loadEpisodesForSeason(state.currentSeasonId, state.selectedSeason);
      }

    }
  }, [state]);

  // Effet pour faire défiler automatiquement la liste des saisons
  useEffect(() => {
    if (activeZone === 'seasons' && seasonsContainerRef.current) {
      const container = seasonsContainerRef.current;
      const selectedElement = container.children[selectedSeason] as HTMLElement;

      if (selectedElement) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = selectedElement.getBoundingClientRect();

        // Vérifier si l'élément sélectionné est partiellement ou totalement hors de vue
        if (elementRect.bottom > containerRect.bottom) {
          // Faire défiler vers le bas si l'élément est en dessous
          container.scrollTop += elementRect.bottom - containerRect.bottom + 10;
        } else if (elementRect.top < containerRect.top) {
          // Faire défiler vers le haut si l'élément est au-dessus
          container.scrollTop -= containerRect.top - elementRect.top + 10;
        }
      }
    }
  }, [selectedSeason, activeZone]);

  // Effet pour faire défiler automatiquement la grille des épisodes
  useEffect(() => {
    if (activeZone === 'episodes' && episodesContainerRef.current) {
      const container = episodesContainerRef.current;
      const selectedElement = container.children[selectedEpisode] as HTMLElement;

      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "center"
        });
      }
    }
  }, [selectedEpisode, activeZone]);

  // Vérifier le progrès de lecture pour tous les épisodes
  useEffect(() => {
    const checkAllEpisodesProgress = async () => {
      if (!episodes || episodes.length === 0) return;
      
      const progressData: Record<string, number> = {};
      
      for (const episode of episodes) {
        if (!episode || !episode.link) continue;
        
        try {
          const savedPosition = await getCachedData<{
            position: number;
            timestamp: number;
            duration: number;
          }>(`movie_position_${episode.link}`);

          if (savedPosition && savedPosition.position > 0 && savedPosition.duration > 0) {
            console.log("Progress found for episode:", episode.name);
            const percent = Math.min(100, Math.round((savedPosition.position / savedPosition.duration) * 100));
            console.log("percent", percent);
            if (percent < 95) {
              progressData[episode.link] = percent;
            }
          }
        } catch (error) {
          console.error('Error checking progress for episode:', error);
        }
      }
      
      setEpisodeProgress(progressData);
    };

    checkAllEpisodesProgress();
  }, [episodes]);

  if (!series) return <div className="notFoundEpg">{t("seriesNotFound")}</div>;
  const currentSeason = seasons[activeSeason] || {};

  // Déterminer quelles informations afficher (saison ou série)
  const displayLogo =  series.back;

  const displayRate = currentSeason.rate || series.rate;
  const displayDate = currentSeason.date || series.date;
  const displayActors = currentSeason.actors || series.actors;
  const displayDescription = currentSeason.description || series.description || t("noDescriptionAvailable");

  return (
    <div className="relative flex flex-col w-full h-screen text-white overflow-hidden" onMouseMove={stopAutoTrailer} onClick={stopAutoTrailer}>
      {/* Image de fond sur toute la page */}
      <div className="absolute inset-0 z-0">
        {/* Image de fond statique */}
        {displayLogo && !isTrailerPlaying && (
          <img
            src={displayLogo}
            className="w-full h-full object-cover"
            alt={series.name}
          />
        )}
        
        {/* Vidéo trailer en arrière-plan */}
        {series.name && (
          <video
            ref={videoRef}
            className={`w-full h-full object-cover ${isTrailerPlaying ? 'block' : 'hidden'}`}
            loop
            playsInline
            preload="metadata"
            onLoadedData={() => {
              // Assurer que la vidéo est prête sans loading visible
              if (videoRef.current) {
                videoRef.current.currentTime = 0;
              }
            }}
          >
            <source src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
          </video>
        )}
        
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
      </div>

      {/* Contenu principal avec z-index élevé */}
      <div className="relative z-10 flex flex-col h-full">
        {/* En-tête avec informations de la série */}
        <div className="h-1/2 flex items-end p-8">
          <div className="w-full">
            <h1 className="text-5xl font-bold mb-4 drop-shadow-lg">{series.name}</h1>
            <div className="flex items-center mb-4">
              {displayRate && <span className="bg-red-600 text-white px-2 py-1 rounded mr-4">{displayRate}</span>}
              {displayDate && <span className="text-gray-300 mr-4 drop-shadow-lg">{displayDate}</span>}
            </div>
            {displayActors && (
              <div className="mb-4 flex items-center gap-4">
                <h2 className="text-2xl font-bold drop-shadow-lg">{t("actors")}</h2>
                <p className="text-xl drop-shadow-lg">{displayActors}</p>
              </div>
            )}
            <p className="text-xl max-w-3xl drop-shadow-lg">{displayDescription}</p>
          </div>
        </div>

        {/* Section des saisons et épisodes avec fond semi-transparent */}
        <div className="flex h-1/2 bg-opacity-40">
          {/* Liste des saisons */}
          <div className="w-1/4 p-6 overflow-hidden">
            <h2 className="text-3xl font-bold mb-6">{t("seasons")}</h2>
            <div
              ref={seasonsContainerRef}
              className="flex flex-col gap-4 overflow-y-auto max-h-[calc(100%-4rem)] pr-2 pb-4 pt-2 pl-2"
            >
              {seasons.map((season: any, index: number) => (
                <div
                  key={season.id}
                  className={`p-3 rounded cursor-pointer transition-all duration-200 ${activeSeason === index
                    ? 'bg-red-600 bg-opacity-90'
                    : 'bg-gray-800 bg-opacity-50'
                    } ${activeZone === 'seasons' && selectedSeason === index
                      ? 'ring-2 ring-white transform scale-[1.02]'
                      : ''
                    }`}
                  onClick={() => {
                    setSelectedSeason(index);
                    setActiveZone('seasons');
                    loadEpisodesForSeason(season.id, index);
                  }}
                >
                  <div className="flex items-center gap-3">
                    {season.logo && (
                      <img
                        src={season.logo}
                        alt={season.name}
                        className="w-16 h-16 object-cover rounded"
                      />
                    )}
                    <div className="flex-1">
                      <p className="text-xl font-medium">{season.name}</p>
                      {season.date && (
                        <p className="text-sm text-gray-300 mt-1">{season.date}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Liste des épisodes */}
          <div className="w-3/4 p-6 overflow-hidden">
            <h2 className="text-3xl font-bold mb-6">{t("episodes")}</h2>
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
              </div>
            ) : episodes.length === 0 ? (
              <p className="text-xl">{t("noEpisodeAvailable")}</p>
            ) : (
              <div
                ref={episodesContainerRef}
                className="flex flex-col gap-3 overflow-y-auto max-h-[calc(100vh-20rem)] p-2 pb-8"
              >
                {episodes.map((episode: any, index: number) => (
                  <div
                    key={episode.id}
                    className={`p-4 rounded-lg cursor-pointer transition-all duration-200 ${
                      activeZone === 'episodes' && selectedEpisode === index
                        ? 'bg-gray-800 bg-opacity-70 ring-4 ring-red-600'
                        : 'bg-gray-800 bg-opacity-50 hover:bg-gray-700 hover:bg-opacity-70'
                    }`}
                    onClick={() => handleEpisodeClick(episode)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2">{episode.name}</h3>
                        {episode.desc && (
                          <p className="text-sm text-gray-300 line-clamp-2">{episode.desc}</p>
                        )}
                        <div className="flex items-center mt-2 gap-4">
                          {episode.duration && (
                            <span className="text-xs bg-gray-600 px-2 py-1 rounded">
                            {episode.duration ? episode.duration.replace('http://ll.ssdlist.xyz:2022', '') : ''}
                            </span>
                          )}
                          {episode.date && (
                            <span className="text-xs text-gray-400">{episode.date}</span>
                          )}
                        </div>
                        
                        {/* Barre de progression si l'épisode a été partiellement visionné */}
                        {episodeProgress[episode.link] && (
                          <div className="mt-2">
                            <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-red-600 rounded-full"
                                style={{ width: `${episodeProgress[episode.link]}%` }}
                              ></div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeriesDescription;