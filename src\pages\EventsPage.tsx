import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { Event, EventChannel } from '../types/events';
import { useTranslation } from '../hooks/useTranslation';
import { RETURN_KEY_CODE } from '../utils/keysCode';
import EventChannelsModal from '../components/EventChannelsModal';

const EventsPage: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [eventsChannels, setEventsChannels] = useState<{ [eventId: string]: EventChannel[] }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.fetchEvents();

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      if (response && response.data && response.data.events) {
        const eventsList = response.data.events;
        setEvents(eventsList);

        // Récupérer les chaînes pour chaque événement
        await fetchAllEventsChannels(eventsList);
      } else {
        setError('No events data received');
      }
    } catch (err) {
      setError('Failed to load events. Please try again later.');
      console.error('Error fetching events:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllEventsChannels = async (eventsList: Event[]) => {
    const channelsData: { [eventId: string]: EventChannel[] } = {};

    // Récupérer les chaînes pour chaque événement en parallèle
    const promises = eventsList.map(async (event) => {
      try {
        const response = await api.fetchEventsChannels(event.id);
        if (typeof response !== 'string' && response?.data?.channels) {
          channelsData[event.id] = response.data.channels;
        }
      } catch (error) {
        console.error(`Error fetching channels for event ${event.id}:`, error);
        channelsData[event.id] = [];
      }
    });

    await Promise.all(promises);
    setEventsChannels(channelsData);
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    // Si le modal est ouvert, ne pas traiter les événements clavier dans EventsPage
    if (modalOpen || loading || events.length === 0) return;

    switch (event.keyCode) {
      case 38: // Up arrow
        event.preventDefault();
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 40: // Down arrow
        event.preventDefault();
        setSelectedIndex(prev => Math.min(events.length - 1, prev + 1));
        break;
      case 13: // Enter
        event.preventDefault();
        handleEventSelect(events[selectedIndex]);
        break;
      case RETURN_KEY_CODE:
      case 10009: // Backspace
        event.preventDefault();
        navigate("/?section=multipacks&item=events");
        break;
    }
  };
  const handleEventSelect = (event: Event) => {
    setSelectedEvent(event);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedEvent(null);
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [loading, events, selectedIndex, modalOpen]);

  // Auto-scroll to selected item
  useEffect(() => {
    if (containerRef.current && events.length > 0) {
      const selectedElement = containerRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  }, [selectedIndex]);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const formatDuration = (duration: string) => {
    const minutes = parseInt(duration);
    if (isNaN(minutes)) return duration;
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}min`;
    }
    return `${minutes}min`;
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-background text-white">
        <div className="w-1/4 bg-sidebar flex items-center justify-center">
          <div className="text-4xl">{t('events')}</div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-2xl">{t('loading')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-background text-white">
        <div className="w-1/4 bg-sidebar flex items-center justify-center">
          <div className="text-4xl">{t('events')}</div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl text-red-500 mb-4">{error}</div>
            <button 
              onClick={fetchEvents}
              className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {t('retry')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="w-1/4 bg-sidebar flex items-center justify-center">
        <div className="text-4xl">{t('events')}</div>
      </div>
      
      <div className="flex-1 p-8">
        {events.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-2xl text-gray-400">{t('no_events')}</div>
          </div>
        ) : (
          <div ref={containerRef} className="space-y-4 max-h-full overflow-y-auto">
            {events.map((event, index) => (
              <div
                key={event.id}
                className={`flex items-center p-6 rounded-lg transition-all duration-200 ${
                  selectedIndex === index
                    ? 'bg-blue-600 scale-105 shadow-lg'
                    : 'bg-gray-800 hover:bg-gray-700'
                }`}
              >
                <div className="flex items-center space-x-6 flex-1">
                  {/* Logos des équipes */}
                  <div className="flex items-center space-x-4">
                    <img
                      src={event.logoL}
                      alt="Team 1"
                      className="w-16 h-16 object-contain"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/NoLogo.png';
                      }}
                    />
                    <span className="text-2xl font-bold">VS</span>
                    <img
                      src={event.logoR}
                      alt="Team 2"
                      className="w-16 h-16 object-contain"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/NoLogo.png';
                      }}
                    />
                  </div>
                  
                  {/* Informations de l'événement */}
                  <div className="flex-1">
                    <h3 className="text-xl font-bold mb-2">{event.name}</h3>
                    <p className="text-gray-300 mb-1">{event.competition}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>{formatDate(event.start)}</span>
                      <span>•</span>
                      <span>{formatDuration(event.duration)}</span>
                    </div>

                    {/* Logos des chaînes */}
                    {eventsChannels[event.id] && eventsChannels[event.id].length > 0 && (
                      <div className="flex items-center space-x-2 mt-3">
                        <span className="text-xs text-gray-500">{t('channels_available')}:</span>
                        <div className="flex space-x-1">
                          {eventsChannels[event.id].slice(0, 5).map((channel) => (
                            <img
                              key={channel.id}
                              src={channel.logo}
                              alt={channel.name}
                              className="w-6 h-6 object-contain rounded"
                              title={channel.name}
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/NoLogo.png';
                              }}
                            />
                          ))}
                          {eventsChannels[event.id].length > 5 && (
                            <span className="text-xs text-gray-500">+{eventsChannels[event.id].length - 5}</span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Indicateur de sélection */}
                {selectedIndex === index && (
                  <div className="ml-4">
                    <i className="fas fa-play text-white text-xl"></i>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal des chaînes d'événements */}
      {selectedEvent && (
        <EventChannelsModal
          eventId={selectedEvent.id}
          eventName={selectedEvent.name}
          isOpen={modalOpen}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default EventsPage;
