export interface MovieCategory {
  id: string;
  name: string;
  logo: string;
  type: string;
  Movies : Movie[];
}

export interface Movie {
  id: string;
  name: string;
  desc: string;
  actors: string;
  back: string;
  logo: string;
  thumb: string;
  date: string;
  link: string;
  date_aj: string;
  duration: string;
  trailer?: string;
  rate: string;
  category: string;
  type: string;
  sub?: string;
  logo_desc?: string;
  language?: string;
}

export interface Category {
  id: string;
  name: string;
  type?: string;
  logo?: string;
  subcategories: any[];
  isSpecial?: boolean;
}

export interface SubCategory {
  id: string;
  name: string;
  type: string;
  logo: string;
  date :string;
  category?: string;
  background: string;
  backlink?: string;
  back?: string;
  link?: string;
  date_aj?: any;
  language?: string;
  sub?: string;
  rate?: string;
}


export interface MovieApiResponse {
  movies: Movie[];
  category: Category[];
}
